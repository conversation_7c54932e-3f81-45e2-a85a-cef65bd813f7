// TaskResults.js - 任务结果页面组件
const { useState, useEffect } = React;

function TaskResults() {
    // 从 localStorage 获取上次保存的选择
    const getStoredBizType = () => {
        try {
            return localStorage.getItem('taskResults_selectedBizType') || '';
        } catch (e) {
            console.warn('无法从 localStorage 读取 bizType:', e);
            return '';
        }
    };

    const getStoredTask = () => {
        try {
            const stored = localStorage.getItem('taskResults_selectedTask');
            return stored ? JSON.parse(stored) : null;
        } catch (e) {
            console.warn('无法从 localStorage 读取 task:', e);
            return null;
        }
    };

    const getStoredRecord = () => {
        try {
            const stored = localStorage.getItem('taskResults_selectedRecord');
            return stored ? JSON.parse(stored) : null;
        } catch (e) {
            console.warn('无法从 localStorage 读取 record:', e);
            return null;
        }
    };

    const getStoredTaskResult = () => {
        try {
            const stored = localStorage.getItem('taskResults_selectedTaskResult');
            return stored ? JSON.parse(stored) : null;
        } catch (e) {
            console.warn('无法从 localStorage 读取 taskResult:', e);
            return null;
        }
    };

    // 状态管理
    const [selectedBizType, setSelectedBizType] = useState(getStoredBizType());
    const [tasks, setTasks] = useState([]);
    const [selectedTask, setSelectedTask] = useState(getStoredTask());
    const [loading, setLoading] = useState(false);
    
    // 执行记录相关状态
    const [runRecords, setRunRecords] = useState([]);
    const [selectedRecord, setSelectedRecord] = useState(getStoredRecord());
    const [loadingRunRecords, setLoadingRunRecords] = useState(false);
    const [runRecordsPage, setRunRecordsPage] = useState({
        current: 1,
        size: 20,
        total: 0
    });

    // 任务结果详情相关状态
    const [taskResultDetails, setTaskResultDetails] = useState(null);
    const [loadingTaskResults, setLoadingTaskResults] = useState(false);
    const [selectedTaskResult, setSelectedTaskResult] = useState(getStoredTaskResult());

    // 重新分析相关状态
    const [reanalyzingBatches, setReanalyzingBatches] = useState(new Set());
    const [refreshInterval, setRefreshInterval] = useState(null);

    // 编辑prompt相关状态
    const [editingPromptItem, setEditingPromptItem] = useState(null);
    const [editingPromptValue, setEditingPromptValue] = useState('');
    const [savingPrompt, setSavingPrompt] = useState(false);
    const [showPromptModal, setShowPromptModal] = useState(false);
    const [modalPromptData, setModalPromptData] = useState(null);

    // 任务执行相关状态
    const [runningTasks, setRunningTasks] = useState(new Set());
    const [debuggingTasks, setDebuggingTasks] = useState(new Set());
    const [debugSkey, setDebugSkey] = useState('');

    // 业务类型选项
    const bizTypeOptions = [
        { value: 'deliver', label: '交付' },
        { value: 'sale', label: '销售' },
        { value: 'customer_service', label: '客户服务' }
    ];

    // 根据业务类型获取任务列表
    const fetchTasksByBizType = async (bizType) => {
        if (!bizType) {
            setTasks([]);
            return;
        }

        try {
            setLoading(true);
            const result = await apiRequest(API_CONFIG.TASK.LIST);

            if (result.success && result.data) {
                const filteredTasks = result.data
                    .filter(task => task.bizType === bizType)
                    .map(task => ({
                        id: task.id,
                        title: task.title || '',
                        bizType: task.bizType || '',
                        templateId: task.templateId || '',
                        cornExp: task.cornExp || '',
                        cycleType: task.cycleType || '',
                        methodId: task.methodId || '',
                        reten: task.reten || 0,
                        coverRepeat: task.coverRepeat || 0,
                        userId: task.userId || '',
                        createdAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : '',
                        updatedAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : ''
                    }));

                setTasks(filteredTasks);
            } else {
                console.error('Invalid response format:', result);
                setTasks([]);
            }
        } catch (err) {
            console.error('Error fetching tasks:', err);
            setTasks([]);
        } finally {
            setLoading(false);
        }
    };

    // 保存到 localStorage
    const saveBizTypeToStorage = (bizType) => {
        try {
            if (bizType) {
                localStorage.setItem('taskResults_selectedBizType', bizType);
            } else {
                localStorage.removeItem('taskResults_selectedBizType');
            }
        } catch (e) {
            console.warn('无法保存 bizType 到 localStorage:', e);
        }
    };

    const saveTaskToStorage = (task) => {
        try {
            if (task) {
                localStorage.setItem('taskResults_selectedTask', JSON.stringify(task));
            } else {
                localStorage.removeItem('taskResults_selectedTask');
            }
        } catch (e) {
            console.warn('无法保存 task 到 localStorage:', e);
        }
    };

    const saveRecordToStorage = (record) => {
        try {
            if (record) {
                localStorage.setItem('taskResults_selectedRecord', JSON.stringify(record));
            } else {
                localStorage.removeItem('taskResults_selectedRecord');
            }
        } catch (e) {
            console.warn('无法保存 record 到 localStorage:', e);
        }
    };

    const saveTaskResultToStorage = (taskResult) => {
        try {
            if (taskResult) {
                localStorage.setItem('taskResults_selectedTaskResult', JSON.stringify(taskResult));
            } else {
                localStorage.removeItem('taskResults_selectedTaskResult');
            }
        } catch (e) {
            console.warn('无法保存 taskResult 到 localStorage:', e);
        }
    };

    // 处理业务类型变化
    const handleBizTypeChange = (bizType) => {
        setSelectedBizType(bizType);
        setSelectedTask(null);
        setSelectedRecord(null);
        setTaskResultDetails(null);
        setSelectedTaskResult(null);
        setRunRecords([]);
        
        // 保存到 localStorage
        saveBizTypeToStorage(bizType);
        saveTaskToStorage(null); // 清空任务选择
        saveRecordToStorage(null); // 清空记录选择
        saveTaskResultToStorage(null); // 清空任务结果选择
        
        // 清理定时器
        if (refreshInterval) {
            clearInterval(refreshInterval);
            setRefreshInterval(null);
        }
        
        fetchTasksByBizType(bizType);
    };

    // 选择任务
    const handleSelectTask = (task) => {
        setSelectedTask(task);
        setSelectedRecord(null);
        setTaskResultDetails(null);
        setSelectedTaskResult(null);
        
        // 保存到 localStorage
        saveTaskToStorage(task);
        
        // 清理定时器
        if (refreshInterval) {
            clearInterval(refreshInterval);
            setRefreshInterval(null);
        }
        
        // 获取执行记录
        fetchRunRecords(task.id, 1, runRecordsPage.size);
    };

    // 分页获取任务执行记录
    const fetchRunRecords = async (taskId, pageNum = 1, pageSize = 20) => {
        try {
            setLoadingRunRecords(true);
            const url = API_CONFIG.TASK.RUN_RECORDS.replace('{taskId}', taskId) + `?pageNum=${pageNum}&pageSize=${pageSize}`;
            const result = await apiRequest(url, { method: 'POST' });

            if (result.success && result.data) {
                const records = result.data.list || [];
                setRunRecords(records);
                setRunRecordsPage({
                    current: result.data.pageNum || pageNum,
                    size: result.data.pageSize || pageSize,
                    total: result.data.total || 0
                });

                // 检查是否需要自动刷新
                const hasRunningTask = records.some(record => record.execStatus === 'RUNNING');
                // 后端数据已经按ID倒序排列，第一个就是最新记录
                const latestRecord = records.length > 0 ? records[0] : null;
                

                // 先清理现有的定时器，避免重复设置
                if (refreshInterval) {
                    console.log('清理现有定时器:', refreshInterval);
                    clearInterval(refreshInterval);
                    setRefreshInterval(null);
                }
            } else {
                console.error('获取执行记录失败:', result.msg);
                setRunRecords([]);
                setRunRecordsPage({ current: 1, size: 20, total: 0 });
            }
        } catch (err) {
            console.error('Error fetching run records:', err);
            setRunRecords([]);
            setRunRecordsPage({ current: 1, size: 20, total: 0 });
        } finally {
            setLoadingRunRecords(false);
        }
    };

    // 获取任务结果详情
    const fetchTaskResultDetails = async (taskId, batchNo) => {
        try {
            setLoadingTaskResults(true);
            const result = await apiRequest(API_CONFIG.TASK.TASK_RESULTS, {}, { taskId, batchNo });

            if (result.success && result.data) {
                setTaskResultDetails(result.data);
                // 默认选择第一个TaskResult
                if (result.data.taskResults && result.data.taskResults.length > 0) {
                    setSelectedTaskResult(result.data.taskResults[0]);
                }
            } else {
                console.error('获取任务结果详情失败:', result.msg);
                setTaskResultDetails(null);
                setSelectedTaskResult(null);
            }
        } catch (err) {
            console.error('Error fetching task result details:', err);
            setTaskResultDetails(null);
            setSelectedTaskResult(null);
        } finally {
            setLoadingTaskResults(false);
        }
    };

    // 处理执行记录点击
    const handleRecordClick = (record) => {
        setSelectedRecord(record);
        setSelectedTaskResult(null);
        
        // 保存到 localStorage
        saveRecordToStorage(record);
        saveTaskResultToStorage(null); // 清空任务结果选择
        
        fetchTaskResultDetails(selectedTask.id, record.batchNo);

        // 注意：不在这里设置定时器，让fetchRunRecords统一管理定时器
        // fetchRunRecords函数会根据所有记录的状态来决定是否需要定时器
    };

    // 根据批次号重新分析任务
    const handleReanalyzeByBatch = async (taskId, batchNo, event) => {
        if (event) {
            event.stopPropagation();
        }

        const confirmed = await window.showConfirm(`确定要重新分析批次 "${batchNo}" 的数据吗？\n\n这将根据该批次的原始数据重新执行分析功能，更新分析结果。`);
        if (!confirmed) return;

        const batchKey = `${taskId}_${batchNo}`;
        try {
            setReanalyzingBatches(prev => new Set(prev).add(batchKey));
            const result = await apiRequest(API_CONFIG.TASK.REANALYZE_BY_BATCH, {}, { taskId, batchNo });

            if (result.success !== false) {
                window.showMessage.success(`批次 "${batchNo}" 重新分析成功！\n${result.data || result.message || ''}`);
                await fetchRunRecords(taskId, runRecordsPage.current, runRecordsPage.size);
                // fetchRunRecords函数内部已经处理了定时器逻辑，这里不需要再设置
            } else {
                throw new Error(result.msg || result.message || '重新分析失败');
            }
        } catch (error) {
            console.error('重新分析批次失败:', error);
            window.showMessage.error(`重新分析批次 "${batchNo}" 失败: ` + error.message);
        } finally {
            setReanalyzingBatches(prev => {
                const newSet = new Set(prev);
                newSet.delete(batchKey);
                return newSet;
            });
        }
    };

    // 处理分页变化
    const handlePageChange = (pageNum) => {
        fetchRunRecords(selectedTask.id, pageNum, runRecordsPage.size);
    };

    // 组件挂载时初始化数据
    useEffect(() => {
        // 如果有存储的业务类型，自动加载对应的任务列表
        if (selectedBizType) {
            fetchTasksByBizType(selectedBizType);
        }
    }, []); // 只在组件挂载时执行一次

    // 当任务列表加载完成时，如果有存储的任务且在当前任务列表中，则自动选择
    useEffect(() => {
        if (tasks.length > 0 && selectedTask && selectedTask.id) {
            // 查找存储的任务是否在当前任务列表中
            const foundTask = tasks.find(t => t.id.toString() === selectedTask.id.toString());
            if (foundTask) {
                // 如果找到了，更新任务信息并获取执行记录
                setSelectedTask(foundTask);
                saveTaskToStorage(foundTask); // 更新存储的任务信息
                fetchRunRecords(foundTask.id, 1, runRecordsPage.size);
            } else {
                // 如果没找到，清空选择
                setSelectedTask(null);
                setSelectedRecord(null);
                setSelectedTaskResult(null);
                saveTaskToStorage(null);
                saveRecordToStorage(null);
                saveTaskResultToStorage(null);
            }
        }
    }, [tasks]); // 当任务列表变化时执行

    // 当执行记录加载完成时，如果有存储的记录且在当前记录列表中，则自动选择
    useEffect(() => {
        if (runRecords.length > 0 && selectedRecord && selectedRecord.id && selectedTask) {
            // 查找存储的记录是否在当前记录列表中
            const foundRecord = runRecords.find(r => r.id.toString() === selectedRecord.id.toString());
            if (foundRecord) {
                // 如果找到了，更新记录信息并获取任务结果详情
                setSelectedRecord(foundRecord);
                saveRecordToStorage(foundRecord); // 更新存储的记录信息
                fetchTaskResultDetails(selectedTask.id, foundRecord.batchNo);
            } else {
                // 如果没找到，清空选择
                setSelectedRecord(null);
                setSelectedTaskResult(null);
                saveRecordToStorage(null);
                saveTaskResultToStorage(null);
            }
        }
    }, [runRecords, selectedTask]); // 当记录列表或任务变化时执行

    // 当任务结果详情加载完成时，如果有存储的任务结果且在当前结果中，则自动选择
    useEffect(() => {
        if (taskResultDetails && taskResultDetails.taskResults && taskResultDetails.taskResults.length > 0 && selectedTaskResult && selectedTaskResult.id) {
            // 查找存储的任务结果是否在当前结果列表中
            const foundTaskResult = taskResultDetails.taskResults.find(tr => tr.id.toString() === selectedTaskResult.id.toString());
            if (foundTaskResult) {
                // 如果找到了，更新任务结果信息
                setSelectedTaskResult(foundTaskResult);
                saveTaskResultToStorage(foundTaskResult); // 更新存储的任务结果信息
            } else {
                // 如果没找到，清空选择
                setSelectedTaskResult(null);
                saveTaskResultToStorage(null);
            }
        }
    }, [taskResultDetails]); // 当任务结果详情变化时执行

    // 组件卸载时清理定时器
    useEffect(() => {
        return () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        };
    }, [refreshInterval]);

    // 执行任务
    const handleRunTask = async () => {
        if (!selectedTask) return;

        try {
            setRunningTasks(prev => new Set(prev).add(selectedTask.id));
            await apiRequest(API_CONFIG.TASK.RUN, {}, { taskId: selectedTask.id });

            window.showMessage.success(`任务 "${selectedTask.title}" 执行成功！`);

            // 等待2秒后刷新执行记录
            setTimeout(async () => {
                await fetchRunRecords(selectedTask.id, runRecordsPage.current, runRecordsPage.size);
                // fetchRunRecords函数内部已经处理了定时器逻辑，这里不需要再设置
            }, 2000);
        } catch (error) {
            console.error('执行任务失败:', error);
            window.showMessage.error(`执行任务 "${selectedTask.title}" 失败: ` + error.message);
        } finally {
            setRunningTasks(prev => {
                const newSet = new Set(prev);
                newSet.delete(selectedTask.id);
                return newSet;
            });
        }
    };

    // Debug任务
    const handleDebugTask = async () => {
        if (!selectedTask) return;

        try {
            setDebuggingTasks(prev => new Set(prev).add(selectedTask.id));
            
            // 处理skey和count的逻辑
            const trimmedSkey = debugSkey.trim();
            const skey = trimmedSkey || null;
            const count = skey === null ? 1 : null; // 如果skey是null，则count为1；否则count为null
            
            // 使用新的接口格式
            const debugData = {
                taskId: selectedTask.id,
                count: count,
                skey: skey
            };
            
            await apiRequest(API_CONFIG.TASK.DEBUG, {
                method: 'POST',
                body: JSON.stringify(debugData)
            });

            window.showMessage.success(`任务 "${selectedTask.title}" Debug执行成功！${skey ? `(skey: ${skey})` : `(执行次数: ${count})`}`);

            // 等待2秒后刷新执行记录
            setTimeout(async () => {
                await fetchRunRecords(selectedTask.id, runRecordsPage.current, runRecordsPage.size);
                // fetchRunRecords函数内部已经处理了定时器逻辑，这里不需要再设置
            }, 2000);
        } catch (error) {
            console.error('Debug任务失败:', error);
            window.showMessage.error(`Debug任务 "${selectedTask.title}" 失败: ` + error.message);
        } finally {
            setDebuggingTasks(prev => {
                const newSet = new Set(prev);
                newSet.delete(selectedTask.id);
                return newSet;
            });
        }
    };

    // 开始编辑prompt - 打开弹框
    const handleEditPrompt = async (item) => {
        try {
            // 根据templateId获取template数据
            const templateResult = await apiRequest(API_CONFIG.TEMPLATE.GET_BY_ID, {
                method: 'POST',
                body: JSON.stringify({ id: item.templateId })
            });

            if (templateResult.success !== false && templateResult.data) {
                setModalPromptData({
                    ...item,
                    editingPrompt: templateResult.data.prompt || '',
                    templateData: templateResult.data // 保存完整的template数据
                });
            } else {
                // 如果获取template失败，使用原有的prompt
                setModalPromptData({
                    ...item,
                    editingPrompt: item.prompt || ''
                });
                window.showMessage.warning('获取模板数据失败，使用当前prompt');
            }
        } catch (error) {
            console.error('获取模板数据失败:', error);
            // 如果获取template失败，使用原有的prompt
            setModalPromptData({
                ...item,
                editingPrompt: item.prompt || ''
            });
            window.showMessage.warning('获取模板数据失败，使用当前prompt');
        }

        setShowPromptModal(true);
    };

    // 处理键盘事件（ESC关闭弹框）
    const handleKeyDown = (event) => {
        if (event.key === 'Escape' && showPromptModal) {
            handleClosePromptModal();
        }
    };

    // 监听键盘事件
    useEffect(() => {
        if (showPromptModal) {
            document.addEventListener('keydown', handleKeyDown);
        } else {
            document.removeEventListener('keydown', handleKeyDown);
        }

        // 清理事件监听器
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [showPromptModal]);

    // 关闭prompt编辑弹框
    const handleClosePromptModal = () => {
        setShowPromptModal(false);
        setModalPromptData(null);
        setSavingPrompt(false);
    };

    // 更新弹框中的prompt值
    const handleModalPromptChange = (value) => {
        setModalPromptData(prev => ({
            ...prev,
            editingPrompt: value
        }));
    };

    // 仅保存prompt（不重新分析）
    const handleSavePromptOnly = async () => {
        if (!modalPromptData || !modalPromptData.editingPrompt.trim()) {
            window.showMessage.warning('Prompt内容不能为空');
            return;
        }

        try {
            setSavingPrompt(true);

            // 调用template的save方法保存prompt
            const templateData = {
                id: modalPromptData.templateId,
                prompt: modalPromptData.editingPrompt
            };

            // 如果有完整的template数据，保留其他字段
            if (modalPromptData.templateData) {
                Object.assign(templateData, {
                    title: modalPromptData.templateData.title,
                    remark: modalPromptData.templateData.remark,
                    bizType: modalPromptData.templateData.bizType,
                    subBizType: modalPromptData.templateData.subBizType,
                    methodId: modalPromptData.templateData.methodId
                });
            }

            const result = await apiRequest(API_CONFIG.TEMPLATE.SAVE, {
                method: 'POST',
                body: JSON.stringify(templateData)
            });

            if (result.success !== false) {
                window.showMessage.success('Prompt保存成功！');

                // 刷新任务结果详情
                await fetchTaskResultDetails(selectedTask.id, selectedRecord.batchNo);

                // 关闭弹框
                handleClosePromptModal();
            } else {
                throw new Error(result.msg || result.message || '保存失败');
            }
        } catch (error) {
            console.error('保存prompt失败:', error);
            window.showMessage.error('保存prompt失败: ' + error.message);
        } finally {
            setSavingPrompt(false);
        }
    };

    // 保存prompt并重新分析
    const handleSavePromptAndReanalyze = async () => {
        if (!modalPromptData || !modalPromptData.editingPrompt.trim()) {
            window.showMessage.warning('Prompt内容不能为空');
            return;
        }

        try {
            setSavingPrompt(true);

            // 第一步：调用template的save方法保存prompt
            const templateData = {
                id: modalPromptData.templateId,
                prompt: modalPromptData.editingPrompt
            };

            // 如果有完整的template数据，保留其他字段
            if (modalPromptData.templateData) {
                Object.assign(templateData, {
                    title: modalPromptData.templateData.title,
                    remark: modalPromptData.templateData.remark,
                    bizType: modalPromptData.templateData.bizType,
                    subBizType: modalPromptData.templateData.subBizType,
                    methodId: modalPromptData.templateData.methodId
                });
            }

            const saveResult = await apiRequest(API_CONFIG.TEMPLATE.SAVE, {
                method: 'POST',
                body: JSON.stringify(templateData)
            });

            if (saveResult.success === false) {
                throw new Error(saveResult.msg || saveResult.message || '保存模板失败');
            }

            // 第二步：调用TASK的REANALYZE_BY_BATCH方法重新分析
            const reanalyzeResult = await apiRequest(API_CONFIG.TASK.REANALYZE_BY_BATCH, {
                method: 'POST'
            }, {
                taskId: selectedTask.id,
                batchNo: selectedRecord.batchNo
            });

            if (reanalyzeResult.success === false) {
                throw new Error(reanalyzeResult.msg || reanalyzeResult.message || '重新分析失败');
            }

            window.showMessage.success('Prompt保存并重新分析成功！');

            // 刷新任务结果详情
            await fetchTaskResultDetails(selectedTask.id, selectedRecord.batchNo);
            
            // 刷新执行记录列表
            await fetchRunRecords(selectedTask.id, runRecordsPage.current, runRecordsPage.size);
            
            // 等待一下让数据更新，然后找到最新的重新分析记录并选中
            setTimeout(async () => {
                try {
                    // 再次获取最新的执行记录
                    const url = API_CONFIG.TASK.RUN_RECORDS.replace('{taskId}', selectedTask.id) + `?pageNum=1&pageSize=${runRecordsPage.size}`;
                    const result = await apiRequest(url, { method: 'POST' });
                    
                    if (result.success && result.data && result.data.list) {
                        const records = result.data.list;
                        // 找到最新的重新分析记录（按时间排序，第一个就是最新的）
                        const latestReanalyzeRecord = records.find(record => 
                            record.execType === 'REANALYZE' && 
                            record.batchNo === selectedRecord.batchNo
                        );
                        
                        if (latestReanalyzeRecord) {
                            // 更新选中的记录为最新的重新分析记录
                            setSelectedRecord(latestReanalyzeRecord);
                            saveRecordToStorage(latestReanalyzeRecord);
                            
                            // 更新执行记录列表
                            setRunRecords(records);
                            setRunRecordsPage({
                                current: result.data.pageNum || 1,
                                size: result.data.pageSize || runRecordsPage.size,
                                total: result.data.total || 0
                            });
                        }
                    }
                } catch (error) {
                    console.error('获取最新执行记录失败:', error);
                }
            }, 1000); // 等待1秒让后端处理完成

            // 更新弹框中的数据，确保更新时间跟随变化
            const updatedDetails = await fetchTaskResultDetailsForModal(selectedTask.id, selectedRecord.batchNo);
            if (updatedDetails) {
                const updatedItem = updatedDetails.taskResultItems?.find(item => item.id === modalPromptData.id);
                if (updatedItem) {
                    // 重新获取最新的template数据
                    try {
                        const templateResult = await apiRequest(API_CONFIG.TEMPLATE.GET_BY_ID, {
                            method: 'POST',
                            body: JSON.stringify({ id: updatedItem.templateId })
                        });

                        setModalPromptData(prev => ({
                            ...prev,
                            itemResult: updatedItem.itemResult,
                            updateTime: updatedItem.updateTime, // 更新时间字段
                            editingPrompt: templateResult.data?.prompt || updatedItem.prompt,
                            templateData: templateResult.data
                        }));
                    } catch (templateError) {
                        console.error('重新获取模板数据失败:', templateError);
                        setModalPromptData(prev => ({
                            ...prev,
                            itemResult: updatedItem.itemResult,
                            updateTime: updatedItem.updateTime, // 更新时间字段
                            editingPrompt: updatedItem.prompt
                        }));
                    }
                }
            }
        } catch (error) {
            console.error('保存prompt并重新分析失败:', error);
            window.showMessage.error('保存prompt并重新分析失败: ' + error.message);
        } finally {
            setSavingPrompt(false);
        }
    };

    // 获取任务结果详情（用于弹框更新）
    const fetchTaskResultDetailsForModal = async (taskId, batchNo) => {
        try {
            const result = await apiRequest(API_CONFIG.TASK.TASK_RESULTS, {}, { taskId, batchNo });
            if (result.success && result.data) {
                return result.data;
            }
        } catch (err) {
            console.error('Error fetching task result details for modal:', err);
        }
        return null;
    };

    // 渲染业务类型和任务选择
    const renderSelectionAndActions = () => (
        <div className="mb-6 flex items-center space-x-4">
            {/* 业务类型选择 */}
            <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">业务类型:</label>
                <select
                    value={selectedBizType}
                    onChange={(e) => handleBizTypeChange(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    <option value="">请选择业务类型</option>
                    {bizTypeOptions.map(option => (
                        <option key={option.value} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>
            </div>

            {/* 任务选择 */}
            <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">任务:</label>
                <select
                    value={selectedTask?.id || ''}
                    onChange={(e) => {
                        const taskId = e.target.value;
                        if (taskId) {
                            const task = tasks.find(t => t.id.toString() === taskId);
                            if (task) {
                                handleSelectTask(task);
                            }
                        } else {
                            setSelectedTask(null);
                            setSelectedRecord(null);
                            setTaskResultDetails(null);
                            setSelectedTaskResult(null);
                            setRunRecords([]);
                            // 清空存储的任务和相关状态
                            saveTaskToStorage(null);
                            saveRecordToStorage(null);
                            saveTaskResultToStorage(null);
                        }
                    }}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!selectedBizType || loading}
                >
                    <option value="">请选择任务</option>
                    {tasks.map(task => (
                        <option key={task.id} value={task.id}>
                            {task.title}
                        </option>
                    ))}
                </select>
                {loading && <span className="text-sm text-gray-500">加载中...</span>}
            </div>

            {/* 执行按钮 */}
            {selectedTask && (
                <div className="flex items-center space-x-2 ml-auto">
                    <button
                        onClick={handleRunTask}
                        disabled={runningTasks.has(selectedTask.id) || debuggingTasks.has(selectedTask.id)}
                        className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                    >
                        {runningTasks.has(selectedTask.id) ? (
                            <span className="flex items-center">
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                执行中
                            </span>
                        ) : (
                            'Run'
                        )}
                    </button>
                    <button
                        onClick={handleDebugTask}
                        disabled={runningTasks.has(selectedTask.id) || debuggingTasks.has(selectedTask.id)}
                        className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                    >
                        {debuggingTasks.has(selectedTask.id) ? (
                            <span className="flex items-center">
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 718-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Debug中
                            </span>
                        ) : (
                            'Debug'
                        )}
                    </button>
                    
                    {/* skey输入框 */}
                    <div className="flex items-center space-x-2">
                        <label className="text-sm font-medium text-gray-700">skey:</label>
                        <input
                            type="text"
                            value={debugSkey}
                            onChange={(e) => setDebugSkey(e.target.value)}
                            className="px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 w-72"
                            placeholder="可选（如UUID等）"
                            disabled={runningTasks.has(selectedTask.id) || debuggingTasks.has(selectedTask.id)}
                        />
                    </div>
                </div>
            )}
        </div>
    );



    // 渲染执行记录列表
    const renderRunRecords = () => {
        if (!selectedBizType) {
            return (
                <div className="text-center py-8 text-gray-500">
                    <p>请先选择业务类型</p>
                </div>
            );
        }

        if (!selectedTask) {
            return (
                <div className="text-center py-8 text-gray-500">
                    <p>请选择任务</p>
                </div>
            );
        }

        if (loadingRunRecords) {
            return (
                <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                    <span className="ml-3 text-gray-500">加载执行记录中...</span>
                </div>
            );
        }

        if (runRecords.length === 0) {
            return (
                <div className="text-center py-8 text-gray-500">
                    <p>暂无执行记录</p>
                </div>
            );
        }

        return (
            <div className="space-y-3">
                {runRecords.map((record, index) => (
                    <div
                        key={record.id}
                        className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                            selectedRecord?.id === record.id
                                ? 'bg-blue-50 border-blue-300 shadow-md'
                                : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
                        }`}
                        onClick={() => handleRecordClick(record)}
                    >
                        <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-2">
                                    <span className="text-sm font-semibold text-gray-900 truncate">
                                        {record.batchNo}
                                    </span>
                                </div>

                                <div className="flex items-center space-x-2 mb-3">
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                        record.execType === 'NORMAL' ? 'bg-blue-100 text-blue-800' :
                                        record.execType === 'DEBUG' ? 'bg-orange-100 text-orange-800' :
                                        'bg-purple-100 text-purple-800'
                                    }`}>
                                        {record.execType === 'NORMAL' ? '正常' :
                                         record.execType === 'DEBUG' ? '调试' : '重新分析'}
                                    </span>
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                        record.execStatus === 'SUCCESS' ? 'bg-green-100 text-green-800' :
                                        record.execStatus === 'FAILED' ? 'bg-red-100 text-red-800' :
                                        'bg-yellow-100 text-yellow-800'
                                    }`}>
                                        {record.execStatus === 'SUCCESS' ? '成功' :
                                         record.execStatus === 'FAILED' ? '失败' : '运行中'}
                                    </span>
                                </div>

                                <div className="text-xs text-gray-500 space-y-1">
                                    <div className="flex items-center">
                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        开始: {record.execStartTime ? new Date(record.execStartTime).toLocaleString() : '未知'}
                                    </div>
                                    {record.execEndTime && (
                                        <div className="flex items-center">
                                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            结束: {new Date(record.execEndTime).toLocaleString()}
                                        </div>
                                    )}
                                    {record.resultCount !== null && (
                                        <div className="flex items-center">
                                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                            处理项数: {record.resultCount}
                                        </div>
                                    )}
                                </div>
                                {record.errorMessage && (
                                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                                        <div className="flex items-start">
                                            <svg className="w-3 h-3 mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span>{record.errorMessage}</span>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {(record.execType === 'NORMAL' || record.execType === 'DEBUG' || record.execType === 'REANALYZE') && record.execStatus === 'SUCCESS' && (
                                <div className="flex-shrink-0 ml-3">
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleReanalyzeByBatch(selectedTask.id, record.batchNo, e);
                                        }}
                                        disabled={reanalyzingBatches.has(`${selectedTask.id}_${record.batchNo}`)}
                                        className="px-3 py-1.5 text-xs bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
                                        title="重新分析此批次的数据"
                                    >
                                        {reanalyzingBatches.has(`${selectedTask.id}_${record.batchNo}`) ? (
                                            <span className="flex items-center">
                                                <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                分析中
                                            </span>
                                        ) : (
                                            <span className="flex items-center">
                                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                </svg>
                                                重新分析
                                            </span>
                                        )}
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                ))}

                {/* 分页 */}
                {runRecordsPage.total > runRecordsPage.size && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-500">
                                显示 {((runRecordsPage.current - 1) * runRecordsPage.size) + 1} - {Math.min(runRecordsPage.current * runRecordsPage.size, runRecordsPage.total)} 条，共 {runRecordsPage.total} 条
                            </div>
                            <div className="flex space-x-1">
                                <button
                                    onClick={() => handlePageChange(runRecordsPage.current - 1)}
                                    disabled={runRecordsPage.current <= 1}
                                    className="px-3 py-1.5 text-sm bg-white border border-gray-300 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                                >
                                    上一页
                                </button>
                                <span className="px-3 py-1.5 text-sm text-gray-600 bg-gray-50 border border-gray-300 rounded-md">
                                    {runRecordsPage.current} / {Math.ceil(runRecordsPage.total / runRecordsPage.size)}
                                </span>
                                <button
                                    onClick={() => handlePageChange(runRecordsPage.current + 1)}
                                    disabled={runRecordsPage.current >= Math.ceil(runRecordsPage.total / runRecordsPage.size)}
                                    className="px-3 py-1.5 text-sm bg-white border border-gray-300 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                                >
                                    下一页
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    // 渲染任务结果详情
    const renderTaskResultDetails = () => {
        if (!selectedRecord) {
            return (
                <div className="text-center py-8 text-gray-500">
                    <p>请先选择执行记录</p>
                </div>
            );
        }

        if (loadingTaskResults) {
            return (
                <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                    <span className="ml-3 text-gray-500">加载结果详情中...</span>
                </div>
            );
        }

        if (!taskResultDetails) {
            return (
                <div className="text-center py-8 text-gray-500">
                    <p>暂无结果详情</p>
                </div>
            );
        }

        const { taskResults, taskResultItems } = taskResultDetails;

        return (
            <div className="h-full flex gap-4">
                {/* 左侧：TaskResult列表 */}
                <div className="w-1/4 flex flex-col">
                    <div className="mb-3">
                        <h5 className="text-sm font-semibold text-gray-900">任务结果</h5>
                        <p className="text-xs text-gray-500">共 {taskResults?.length || 0} 条</p>
                    </div>

                    <div className="flex-1 overflow-y-auto space-y-2">
                        {taskResults && taskResults.length > 0 ? (
                            taskResults.map((result, index) => (
                                <div
                                    key={result.id}
                                    className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                                        selectedTaskResult?.id === result.id
                                            ? 'bg-blue-50 border-blue-300 shadow-sm'
                                            : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
                                    }`}
                                    onClick={() => {
                                        setSelectedTaskResult(result);
                                        saveTaskResultToStorage(result);
                                    }}
                                >
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium text-gray-900">
                                                {result.skey || `结果 #${index + 1}`}
                                            </span>
                                            {result.role && (
                                                <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                                    result.role === 'salesman' ? 'bg-green-100 text-green-800' :
                                                    result.role === 'group' ? 'bg-blue-100 text-blue-800' :
                                                    result.role === 'manager' ? 'bg-purple-100 text-purple-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {result.role === 'salesman' ? '业务员' :
                                                     result.role === 'group' ? '团长' :
                                                     result.role === 'manager' ? '总经理' : result.role}
                                                </span>
                                            )}
                                        </div>

                                        <div className="text-xs text-gray-500 space-y-1">
                                            {result.dataDate && (
                                                <div className="flex items-center">
                                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                    数据日期: {result.dataDate}
                                                </div>
                                            )}
                                            <div className="flex items-center">
                                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                创建: {result.createTime ? new Date(result.createTime).toLocaleString() : '未知'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                <p className="text-sm">暂无任务结果</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* 右侧：TaskResultItem列表 */}
                <div className="w-3/4 flex flex-col">
                    <div className="mb-3">
                        <h5 className="text-sm font-semibold text-gray-900">分析结果</h5>
                        {selectedTaskResult ? (
                            <p className="text-xs text-gray-500">
                                {selectedTaskResult.skey || '未命名结果'} 的详细分析
                            </p>
                        ) : (
                            <p className="text-xs text-gray-500">选择左侧任务结果查看详情</p>
                        )}
                    </div>

                    <div className="flex-1 overflow-y-auto">
                        {selectedTaskResult ? (
                            <div className="space-y-3">
                                {taskResultItems
                                    ?.filter(item => item.resultId === selectedTaskResult.id)
                                    ?.map((item, index) => (
                                        <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                            <div className="flex items-center justify-between mb-3">
                                                <div className="flex items-center space-x-2">
                                                    <span className="text-sm font-medium text-gray-900">
                                                        分析项 #{index + 1}
                                                    </span>
                                                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                                        item.subBizType === 'summary' ? 'bg-blue-100 text-blue-800' :
                                                        item.subBizType === 'product' ? 'bg-green-100 text-green-800' :
                                                        item.subBizType === 'area' ? 'bg-yellow-100 text-yellow-800' :
                                                        item.subBizType === 'team' ? 'bg-purple-100 text-purple-800' :
                                                        item.subBizType === 'customer' ? 'bg-pink-100 text-pink-800' :
                                                        'bg-gray-100 text-gray-800'
                                                    }`}>
                                                        {item.subBizType}
                                                    </span>
                                                </div>
                                                <span className="text-xs text-gray-500">ID: {item.id}</span>
                                            </div>

                                            {/* 时间信息 */}
                                            <div className="mb-3 text-xs text-gray-500 space-y-1">
                                                <div className="flex items-center">
                                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    创建: {item.createTime ? new Date(item.createTime).toLocaleString() : '未知'}
                                                </div>
                                                {item.updateTime && (
                                                    <div className="flex items-center">
                                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                        </svg>
                                                        更新: {new Date(item.updateTime).toLocaleString()}
                                                    </div>
                                                )}
                                            </div>

                                            {/* 编辑Prompt按钮 */}
                                            <div className="mb-3">
                                                <button
                                                    onClick={() => handleEditPrompt(item)}
                                                    className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                                                >
                                                    编辑Prompt
                                                </button>
                                            </div>

                                            {/* 分析结果 */}
                                            {item.itemResult ? (
                                                <div className="mb-3">
                                                    <div className="text-xs font-medium text-gray-700 mb-1">分析结果</div>
                                                    <div
                                                        className="markdown-content"
                                                        dangerouslySetInnerHTML={{
                                                            __html: marked.parse(item.itemResult || '')
                                                        }}
                                                    />
                                                </div>
                                            ) : (
                                                <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-center mb-3">
                                                    <svg className="w-6 h-6 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <p className="text-sm text-gray-500">暂无分析结果</p>
                                                </div>
                                            )}

                                            {/* 原始数据 */}
                                            {item.originData ? (
                                                <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                                                    <div className="flex items-start">
                                                        <svg className="w-4 h-4 mr-2 mt-0.5 text-gray-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                        <div className="flex-1">
                                                            <div className="text-xs font-medium text-gray-700 mb-2">原始数据</div>
                                                            <div className="bg-white border border-gray-200 rounded p-2 max-h-40 overflow-y-auto">
                                                                <pre className="text-xs text-gray-800 whitespace-pre-wrap font-mono leading-relaxed">
                                                                    {(() => {
                                                                        try {
                                                                            const parsed = JSON.parse(item.originData);
                                                                            return JSON.stringify(parsed, null, 2);
                                                                        } catch (e) {
                                                                            return item.originData;
                                                                        }
                                                                    })()}
                                                                </pre>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-center">
                                                    <svg className="w-6 h-6 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                                    </svg>
                                                    <p className="text-sm text-gray-500">暂无原始数据</p>
                                                </div>
                                            )}
                                        </div>
                                    ))}

                                {(!taskResultItems || taskResultItems.filter(item => item.resultId === selectedTaskResult.id).length === 0) && (
                                    <div className="text-center py-12 text-gray-500">
                                        <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <p>该任务结果暂无分析项</p>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center h-full text-gray-500">
                                <svg className="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                                </svg>
                                <p className="text-lg font-medium mb-2">选择任务结果</p>
                                <p className="text-sm">点击左侧任务结果查看详细分析</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    // 渲染Prompt编辑弹框
    const renderPromptModal = () => {
        if (!showPromptModal || !modalPromptData) return null;

        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
                    <div className="p-6">
                        {/* 标题 */}
                        <div className="flex items-center justify-between mb-4">
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900">
                                    编辑Prompt - 分析项 #{modalPromptData.subBizType}
                                </h3>
                                {modalPromptData.templateData && (
                                    <p className="text-sm text-gray-500 mt-1">
                                        模板: {modalPromptData.templateData.title || `ID: ${modalPromptData.templateId}`}
                                    </p>
                                )}
                            </div>
                            <button
                                onClick={handleClosePromptModal}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        {/* 第一行：Prompt编辑区域 */}
                        <div className="mb-6">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                提示词
                                <span className="text-xs text-gray-500 ml-2">
                                    (来自模板，修改将更新模板内容)
                                </span>
                            </label>
                            <textarea
                                value={modalPromptData.editingPrompt}
                                onChange={(e) => handleModalPromptChange(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                style={{ height: '400px' }}
                                placeholder="输入Prompt内容"
                            />
                        </div>

                        {/* 第二行：原始数据和分析结果 */}
                        <div className="grid grid-cols-2 gap-6 mb-6">
                            {/* 左侧：原始数据 */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">原始数据</label>
                                <div className="bg-gray-50 border border-gray-200 rounded-md p-3 h-64 overflow-y-auto">
                                    <pre className="text-xs text-gray-800 whitespace-pre-wrap font-mono leading-relaxed">
                                        {(() => {
                                            try {
                                                if (modalPromptData.originData) {
                                                    const parsed = JSON.parse(modalPromptData.originData);
                                                    return JSON.stringify(parsed, null, 2);
                                                }
                                                return '暂无原始数据';
                                            } catch (e) {
                                                return modalPromptData.originData || '暂无原始数据';
                                            }
                                        })()}
                                    </pre>
                                </div>
                            </div>

                            {/* 右侧：分析结果 */}
                            <div>
                                <div className="flex items-center justify-between mb-2">
                                    <label className="block text-sm font-medium text-gray-700">分析结果</label>
                                    {modalPromptData.updateTime && (
                                        <span className="text-xs text-gray-500">
                                            更新时间: {new Date(modalPromptData.updateTime).toLocaleString()}
                                        </span>
                                    )}
                                </div>
                                <div className="border border-gray-200 rounded-md p-3 h-64 overflow-y-auto">
                                    {modalPromptData.itemResult ? (
                                        <div
                                            className="markdown-content"
                                            dangerouslySetInnerHTML={{
                                                __html: marked.parse(modalPromptData.itemResult || '')
                                            }}
                                        />
                                    ) : (
                                        <div className="text-center py-8 text-gray-500">
                                            <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p className="text-sm">暂无分析结果</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* 按钮区域 */}
                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={handleClosePromptModal}
                                disabled={savingPrompt}
                                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed"
                            >
                                取消
                            </button>
                            <button
                                onClick={handleSavePromptOnly}
                                disabled={savingPrompt}
                                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                            >
                                {savingPrompt ? '保存中...' : '保存模板'}
                            </button>
                            <button
                                onClick={handleSavePromptAndReanalyze}
                                disabled={savingPrompt}
                                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                            >
                                {savingPrompt ? '分析中...' : '保存模板并重新分析'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="mb-6">
                <h2 className="text-2xl font-bold mb-4">任务结果</h2>
                {renderSelectionAndActions()}
            </div>

            <div className="flex-1 grid grid-cols-12 gap-6 h-full">
                {/* 执行记录列表 */}
                <div className="col-span-4 bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden">
                    <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="font-medium text-sm">执行记录</h3>
                                <p className="text-xs text-gray-500 mt-1">
                                    {selectedTask ? `${selectedTask.title} 的执行历史` : '请选择任务'}
                                </p>
                            </div>
                            {selectedTask && (
                                <button
                                    onClick={() => fetchRunRecords(selectedTask.id, runRecordsPage.current, runRecordsPage.size)}
                                    disabled={loadingRunRecords}
                                    className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="刷新执行记录"
                                >
                                    <svg className={`w-4 h-4 ${loadingRunRecords ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                </button>
                            )}
                        </div>
                    </div>
                    <div className="p-4 overflow-y-auto" style={{ maxHeight: 'calc(100vh - 280px)' }}>
                        {renderRunRecords()}
                    </div>
                </div>

                {/* 任务结果详情 */}
                <div className="col-span-8 bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden">
                    <div className="p-4 border-b border-gray-200">
                        <h3 className="font-medium text-sm">结果详情</h3>
                        <p className="text-xs text-gray-500 mt-1">
                            {selectedRecord ? `批次: ${selectedRecord.batchNo}` : '请选择执行记录'}
                        </p>
                    </div>
                    <div className="p-4 h-full overflow-hidden">
                        <div className="h-full" style={{ maxHeight: 'calc(100vh - 280px)' }}>
                            {renderTaskResultDetails()}
                        </div>
                    </div>
                </div>
            </div>

            {/* Prompt编辑弹框 */}
            {renderPromptModal()}
        </div>
    );
}

// 导出组件
window.TaskResults = TaskResults;
