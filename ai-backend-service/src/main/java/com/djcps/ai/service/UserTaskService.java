package com.djcps.ai.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.core.constants.CommonConstants;
import com.djcps.ai.core.vo.DateRange;
import com.djcps.ai.dao.dto.TaskResultDto;
import com.djcps.ai.dao.entity.TaskInvokeMethod;
import com.djcps.ai.dao.entity.UserTask;
import com.djcps.ai.dao.entity.UserTaskRunRecord;
import com.djcps.ai.dao.entity.UserTemplate;
import com.djcps.ai.dao.mapper.UserTaskMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.djcps.ai.core.vo.DateRange.getDateDate;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserTaskService extends ServiceImpl<UserTaskMapper, UserTask> {

    private final UserTemplateService userTemplateService;
    private final TaskResultService taskResultService;
    private final UserTaskRunRecordService userTaskRunRecordService;
    private final TaskInvokeMethodService methodService;
    // 线程池用于异步执行
    private final Executor userTaskRunExecutor;

    @Async
    public void runTask(Long taskId, Integer count,String filterSkey,String filterSalerId) {
        Long runRecordId = null;
        String batchNo = IdUtil.simpleUUID();

        try {
            UserTask task = getById(taskId);
            if (task == null) {
                log.info("当前xxlJob调用的id为:{}的 userTask未找到", taskId);
                return;
            }

            // 创建任务执行记录
            runRecordId = userTaskRunRecordService.createRunRecord(taskId, batchNo,
                    count != null ? UserTaskRunRecord.ExecType.DEBUG : UserTaskRunRecord.ExecType.NORMAL);

            String templateId = task.getTemplateId();
            if (StrUtil.isBlankIfStr(templateId)) {
                log.info("当前xxlJob调用的id为:{}的 userTask对应的模板id为空", taskId);
                if (runRecordId != null) {
                    userTaskRunRecordService.updateRecordFailed(runRecordId, "模板ID为空");
                }
                return;
            }
            List<Long> tempIdStrList = StrUtil.split(templateId, ",", true, true).stream().map(Long::parseLong).toList();
            List<UserTemplate> templateList = new ArrayList<>(CollUtil.size(tempIdStrList));
            List<Long> methodIdList = new ArrayList<>();
            for (Long id : tempIdStrList) {
                UserTemplate template = userTemplateService.getById(id);
                if (template == null) {
                    log.info("当前xxlJob调用的id为:{}的 userTask对应的模板,id为{}未找到", taskId, templateId);
                    if (runRecordId != null) {
                        userTaskRunRecordService.updateRecordFailed(runRecordId, "模板未找到: " + id);
                    }
                    return;
                }
                methodIdList.add(template.getMethodId());
                templateList.add(template);
            }
            Map<Long, TaskInvokeMethod>  methodMap = methodService.listByIds(methodIdList).stream().collect(Collectors.toMap(TaskInvokeMethod::getId, v -> v));

            TaskInvokeMethod analysisMethod = methodService.getAnalysisMethod();
            //获取参数并校验
            Long taskMethodId = task.getMethodId();
            if (StrUtil.isBlankIfStr(taskMethodId)) {
                log.info("当前xxlJob调用的id为:{}的 taskMethodId:{} 参数列表为空", taskId, taskMethodId);
                if (runRecordId != null) {
                    userTaskRunRecordService.updateRecordFailed(runRecordId, "方法ID为空");
                }
                return;
            }

            List<Object> invokeResponseList = methodService.fetchScopeParamList(taskId, taskMethodId, batchNo);
            if (CollUtil.isEmpty(invokeResponseList)) {
                log.info("当前xxlJob调用的id为:{}的 taskMethodId 参数列表 值为空", taskId);
                if (runRecordId != null) {
                    userTaskRunRecordService.updateRecordFailed(runRecordId, "参数列表为空");
                }
                return;
            }
            String cycleType = task.getCycleType();
            String bizType = task.getBizType();
            Date now;
            if (count != null && count>0) {
                invokeResponseList = CollUtil.sub(invokeResponseList, 0, count);
                log.info("当前xxlJob调用的id为:{}的 userTask参数列表中,只执行前{}个任务", taskId, count);
            }
            if (StrUtil.isNotBlank(filterSkey)) {
                invokeResponseList = invokeResponseList.stream()
                        .filter(item -> {
                            JSONObject jsonObject = JSONUtil.parseObj(item);
                            String skey = jsonObject.getByPath("skey", String.class);
                            return StrUtil.equals(skey, filterSkey);
                        })
                        .collect(Collectors.toList());
                log.info("当前xxlJob调用的id为:{}的 userTask参数列表中,只执行skey为{}的任务", taskId, filterSkey);
            }
            if (StrUtil.isNotBlank(filterSalerId)) {
                invokeResponseList = invokeResponseList.stream()
                        .filter(item -> {
                            JSONObject jsonObject = JSONUtil.parseObj(item);
                            String salerIdItem = jsonObject.getByPath("saler_id", String.class);
                            return StrUtil.equals(salerIdItem, filterSalerId);
                        })
                        .collect(Collectors.toList());
                log.info("当前xxlJob调用的id为:{}的 userTask参数列表中,只执行saler_id为{}的任务", taskId, filterSalerId);
            }
            for (int i = 0; i < invokeResponseList.size(); i++) {
                log.debug("当前执行第{}个任务,共{}个任务", i, invokeResponseList.size());
                Object o = invokeResponseList.get(i);
                JSONObject item = JSONUtil.parseObj(o);
                try {
                    now = new Date();
                    Object skey = item.getByPath("skey");
                    String skeyString = skey.toString();
                    if (skey == null) {
                        log.info("当前xxlJob调用的id为:{}的 userTask参数列表中没有skey", taskId);
                        continue;
                    }

                    Integer repeat1 = task.getCoverRepeat();
                    if (repeat1 == null) {
                        repeat1 = 0;
                    }
                    int repeat = repeat1 + 1;

                    for (int d = 0; d < repeat; d++) {
                        DateRange dateRange = DateRange.get(d, cycleType);
                        item.putByPath("start_time", dateRange.getStart().format(CommonConstants.FULL_DATE_TIME));
                        item.putByPath("end_time", dateRange.getEnd().format(CommonConstants.FULL_DATE_TIME));
                        item.putByPath("cycle_type", cycleType);
                        item.putByPath("time_dim", cycleType);
                        //dify不支持单个传递数组类型的,只能一把传入,到dify再解析
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.putByPath("pre_param", JSONUtil.toJsonStr(item));


                        log.debug("next_method_prepare_param: {}", item);
                        String dateDate = getDateDate(cycleType, dateRange.getStart());
                        String supplierId = item.getByPath("supplier_id", String.class);
                        String salerId = item.getByPath("saler_id", String.class);
                        String orgId = item.getByPath("org_id", String.class);

                        if (1 != task.getReten()) {
                            taskResultService.removeByTemplateDataDate(skeyString, taskId, dateDate, cycleType,supplierId,salerId,orgId);
                        }

                        TaskResultDto taskResult = new TaskResultDto();
                        taskResult.setSkey(skeyString);
                        taskResult.setTaskId(taskId);
                        taskResult.setDataDate(dateDate);
                        taskResult.setBizType(bizType);
                        taskResult.setCycleType(cycleType);
                        taskResult.setExecStartTime(now);
                        taskResult.setBatchNo(batchNo);
                        taskResult.setExecEndTime(new Date());
                        taskResult.setUserId(salerId);
                        taskResult.setOrgId(orgId);
                        taskResult.setSupplierId(supplierId);
                        Long resultId=taskResultService.saveTask(taskResult);
                        taskResult.setId(resultId);
                        List<CompletableFuture<List>> futures = new ArrayList<>();
                        for (UserTemplate template : templateList) {
                            CompletableFuture<List> fetchDataFuture = CompletableFuture.supplyAsync(() -> {
                                List originData = null;
                                try {
                                    Long templateMethodId = template.getMethodId();
                                    TaskInvokeMethod method = methodMap.get(templateMethodId);
                                    if (method == null) {
                                        log.info("当前xxlJob调用的id为:{}的 userTask对应的模板,id为{}未找到", taskId, templateId);
                                        return null;
                                    }
                                    originData = methodService.invokeMethod(taskId, method, batchNo, skeyString, jsonObject);
                                    String join = CollUtil.join(originData, ";");
                                    Long taskItemId = taskResultService.saveResultItem(taskResult, template, batchNo, join);
                                    String prompt = template.getPrompt();
                                    String analysisResult = methodService.invokeAnalysisMethod(taskId, batchNo + "_" + taskItemId, skeyString, analysisMethod, prompt, join,method.getOutputScheme());
                                    taskResultService.updateResultItem(taskItemId, analysisResult,prompt);
                                } catch (Exception e) {
                                    log.error(e.getMessage(), e);
                                }
                                return originData;
                            }, userTaskRunExecutor);
                            futures.add(fetchDataFuture);
                        }
                        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                        allFutures.join(); // 等待所有任务完成
                    }
                } catch (Exception e) {
                    log.error("执行任务失败", e);
                }
                log.info("任务执行情况:总计{}个,当前执行:{}", CollUtil.size(invokeResponseList), i);
            }

            // 更新执行记录为成功状态
            if (runRecordId != null) {
                userTaskRunRecordService.updateRecordSuccess(runRecordId, invokeResponseList.size());
            }

            log.info("任务执行完成");
        } catch (Exception e) {
            log.error("执行任务失败", e);
            // 更新执行记录为失败状态
            if (runRecordId != null) {
                try {
                    userTaskRunRecordService.updateRecordFailed(runRecordId, e.getMessage());
                } catch (Exception updateException) {
                    log.error("更新任务执行记录失败状态时出错", updateException);
                }
            }
        }
    }


}
